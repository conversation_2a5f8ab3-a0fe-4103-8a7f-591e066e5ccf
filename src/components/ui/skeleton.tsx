"use client";

import { cn } from "@/lib/utils";

interface SkeletonProps {
  className?: string;
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-muted/50",
        className
      )}
    />
  );
}

// Specialized skeleton components for common UI patterns

export function SkeletonText({ 
  lines = 1, 
  className 
}: { 
  lines?: number; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton 
          key={i} 
          className={cn(
            "h-4",
            i === lines - 1 && lines > 1 ? "w-3/4" : "w-full"
          )} 
        />
      ))}
    </div>
  );
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-3", className)}>
      <Skeleton className="h-4 w-3/4" />
      <SkeletonText lines={2} />
      <div className="flex gap-2">
        <Skeleton className="h-6 w-16 rounded-full" />
        <Skeleton className="h-6 w-20 rounded-full" />
      </div>
    </div>
  );
}

export function SkeletonAvatar({ 
  size = "md",
  className 
}: { 
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10", 
    lg: "h-12 w-12"
  };

  return (
    <Skeleton 
      className={cn(
        "rounded-full",
        sizeClasses[size],
        className
      )} 
    />
  );
}

export function SkeletonButton({ 
  size = "md",
  className 
}: { 
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const sizeClasses = {
    sm: "h-8 w-16",
    md: "h-10 w-20",
    lg: "h-12 w-24"
  };

  return (
    <Skeleton 
      className={cn(
        "rounded-md",
        sizeClasses[size],
        className
      )} 
    />
  );
}

export function SkeletonList({ 
  items = 3,
  className 
}: { 
  items?: number;
  className?: string;
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: items }).map((_, i) => (
        <SkeletonCard key={i} />
      ))}
    </div>
  );
}

export function SkeletonNavigation({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-between px-4 h-12", className)}>
      <div className="flex items-center gap-4">
        <Skeleton className="h-8 w-8 rounded-lg" />
        <div className="hidden md:flex gap-6">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-12" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
      <SkeletonAvatar size="sm" />
    </div>
  );
}

export function SkeletonSidebar({
  isCollapsed = false,
  className
}: {
  isCollapsed?: boolean;
  className?: string;
}) {
  return (
    <aside className={cn(
      "fixed left-0 top-0 z-40 h-full bg-background border-r border-border/50",
      isCollapsed ? "w-16" : "w-56",
      className
    )}>
      <div className="flex flex-col h-full">
        {/* Header with logo */}
        <div className="flex items-center justify-center p-4 border-b border-border/50">
          <Skeleton className={cn(
            "rounded",
            isCollapsed ? "w-10 h-10" : "w-32 h-12"
          )} />
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center gap-3 px-3 py-2">
              <Skeleton className="h-6 w-6 rounded" />
              {!isCollapsed && <Skeleton className="h-4 w-16" />}
            </div>
          ))}
        </nav>

        {/* Space selector */}
        <div className="p-4 border-t border-border/50">
          <div className="flex items-center gap-3 px-3 py-2">
            <Skeleton className="h-6 w-6 rounded" />
            {!isCollapsed && <Skeleton className="h-4 w-20" />}
          </div>
        </div>

        {/* User menu */}
        <div className="p-4 border-t border-border/50">
          <div className="flex items-center gap-3 px-3 py-2">
            <SkeletonAvatar size="sm" />
            {!isCollapsed && <Skeleton className="h-4 w-16" />}
          </div>
        </div>
      </div>
    </aside>
  );
}

export function SkeletonTaskCard({ className }: { className?: string }) {
  return (
    <div className={cn("py-2 pl-5 md:pl-4 pr-12 md:pr-14 space-y-3 border rounded-lg relative", className)}>
      <div className="flex items-start gap-3">
        <div className="flex flex-col items-center flex-shrink-0 justify-center relative min-h-full">
          <Skeleton className="h-4 w-4 mb-2" />
          <Skeleton className="h-6 w-6 rounded" />
        </div>
        <div className="flex-1 min-w-0 space-y-2">
          <SkeletonText lines={1} />
          <SkeletonText lines={2} />
          <div className="flex items-center gap-1">
            <Skeleton className="h-3.5 w-3.5" />
            <Skeleton className="h-3 w-20" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-5 w-12 rounded-full" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>
      </div>
      {/* Action Icons Skeleton */}
      <div className="absolute top-1/2 right-2 md:right-3 transform -translate-y-1/2 flex flex-col gap-1">
        <Skeleton className="h-8 w-8 rounded" />
        <Skeleton className="h-8 w-8 rounded" />
        <Skeleton className="h-8 w-8 rounded" />
      </div>
    </div>
  );
}

export function SkeletonListNavigation({
  className,
  tabCount = 3
}: {
  className?: string;
  tabCount?: number;
}) {
  return (
    <div className={cn("px-4 py-3 bg-background border-b border-border", className)}>
      <div className="flex items-center justify-between gap-4">
        {/* Left side - Undo/Redo controls */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <Skeleton className="h-8 w-8 rounded" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>

        {/* Center - Scrollable tabs */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-1 overflow-x-auto scrollbar-hide">
            {Array.from({ length: tabCount }).map((_, i) => (
              <div key={i} className="flex items-center gap-1 px-3 py-1.5 rounded-lg flex-shrink-0">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-6 rounded-full" />
              </div>
            ))}
            {/* Add List Button Skeleton */}
            <div className="flex-shrink-0 ml-1">
              <Skeleton className="h-8 w-20 rounded" />
            </div>
          </div>
        </div>

        {/* Right side - Edit toggle */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <Skeleton className="h-8 w-16 rounded" />
        </div>
      </div>
    </div>
  );
}

export function SkeletonUnifiedHeader({
  className,
  isDesktop = false
}: {
  className?: string;
  isDesktop?: boolean;
}) {
  return (
    <div className={cn(
      "sticky top-0 z-50 bg-background",
      isDesktop ? "w-full" : "w-screen",
      className
    )}>
      {/* Top Navigation Skeleton - only show on mobile */}
      {!isDesktop && (
        <div className="relative border-b border-border/50">
          <SkeletonNavigation />
        </div>
      )}

      {/* List Navigation Skeleton */}
      <SkeletonListNavigation />
    </div>
  );
}

export function SkeletonQuickAdd({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center gap-3 p-3 border rounded-lg", className)}>
      <Skeleton className="h-6 w-6 rounded-full flex-shrink-0" />
      <Skeleton className="h-4 flex-1" />
    </div>
  );
}

export function SkeletonPageContent({
  className,
  isDesktop = false
}: {
  className?: string;
  isDesktop?: boolean;
}) {
  return (
    <div className={cn("px-2 md:px-4 py-3 space-y-6", className)}>
      <div className="gradient-border-muted rounded-lg">
        <div className="p-4 space-y-4">
          {/* Header skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-5 w-8 rounded-full" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>

          {/* Task cards skeleton */}
          <div className="space-y-4">
            <SkeletonTaskCard />
            <SkeletonTaskCard />
            <SkeletonTaskCard />
          </div>

          {/* Quick add skeleton */}
          <SkeletonQuickAdd />
        </div>
      </div>
    </div>
  );
}

export function SkeletonDesktopLayout({
  isCollapsed = false,
  isChatOpen = false,
  chatWidth = 320,
  className
}: {
  isCollapsed?: boolean;
  isChatOpen?: boolean;
  chatWidth?: number;
  className?: string;
}) {
  return (
    <div className={cn("flex min-h-screen", className)}>
      {/* Desktop Sidebar Skeleton */}
      <SkeletonSidebar isCollapsed={isCollapsed} />

      {/* Main Content Area */}
      <div
        className={cn(
          "flex min-h-screen flex-col flex-1 transition-all duration-300 ease-out",
          "ml-56",
          isCollapsed && "ml-16"
        )}
        style={{
          marginRight: isChatOpen ? `${chatWidth}px` : '0px'
        }}
      >
        {/* Unified Header Skeleton */}
        <SkeletonUnifiedHeader isDesktop={true} />

        {/* Main Content */}
        <main className="flex-1">
          <SkeletonPageContent isDesktop={true} />
        </main>
      </div>

      {/* Chat Sidebar Skeleton */}
      {isChatOpen && (
        <aside
          className="fixed right-0 top-0 z-40 h-full bg-background border-l border-border/50"
          style={{ width: `${chatWidth}px` }}
        >
          <div className="p-4 border-b border-border/50">
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="p-4 space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        </aside>
      )}
    </div>
  );
}

export function SkeletonListTitle({
  className,
  width = "w-32"
}: {
  className?: string;
  width?: string;
}) {
  return (
    <Skeleton
      className={cn(
        "h-7 rounded-md", // h-7 matches text-lg line height
        width,
        className
      )}
    />
  );
}

export function SkeletonListTitleWithPlaceholder({
  className,
  placeholderText = "My Tasks"
}: {
  className?: string;
  placeholderText?: string;
}) {
  return (
    <div
      className={cn(
        "h-7 text-lg font-semibold text-muted-foreground/40 animate-pulse",
        "cursor-pointer select-none",
        className
      )}
    >
      {placeholderText}
    </div>
  );
}

export function SkeletonListTitleWithDots({
  className
}: {
  className?: string;
}) {
  return (
    <div
      className={cn(
        "h-7 flex items-center text-lg font-semibold text-muted-foreground/60",
        className
      )}
    >
      <span className="animate-pulse">Loading</span>
      <span className="ml-1 flex">
        <span className="animate-bounce" style={{ animationDelay: '0ms' }}>.</span>
        <span className="animate-bounce" style={{ animationDelay: '150ms' }}>.</span>
        <span className="animate-bounce" style={{ animationDelay: '300ms' }}>.</span>
      </span>
    </div>
  );
}
