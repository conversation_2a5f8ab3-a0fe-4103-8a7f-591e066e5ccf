"use client";

import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChatToggleButtonProps {
  isOpen: boolean;
  onClick: () => void;
  className?: string;
  chatWidth?: number;
}

export function ChatToggleButton({ isOpen, onClick, className, chatWidth = 320 }: ChatToggleButtonProps) {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onClick}
      className={cn(
        "fixed z-30 h-10 w-10 p-0 bg-background/80 backdrop-blur-sm border-border/50 hover:bg-muted/50",
        "top-[80vh] -translate-y-1/2", // Position at 80% of viewport height, centered
        className
      )}
      style={{
        right: isOpen ? `${chatWidth + 16}px` : '16px', // Dynamic positioning based on chat width
        transition: 'right 300ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth positioning transition
      }}
      title={isOpen ? "Close chat" : "Open chat"}
    >
      <MessageCircle className={cn("h-4 w-4", isOpen && "text-primary")} />
    </Button>
  );
}
